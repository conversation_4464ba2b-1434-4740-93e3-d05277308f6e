# 語音AI對談網站

## 描述

本產品為一個基於網頁的語音AI對談應用程式，使用者透過語音與後端AI模型進行互動，實現自然、直覺的語音對談體驗。此產品旨在提供便捷、高效的AI語音助理服務，適用於需要快速獲取資訊或完成任務的使用者。

### 核心功能
* **語音輸入:** 使用者透過瀏覽器內建麥克風進行語音輸入。
* **語音辨識 (STT):** 將使用者語音轉換成文字 (使用 Google Cloud Speech-to-Text)。
* **AI對談引擎:** 將文字輸入傳送給大型語言模型 (LLM)，並接收其回覆 (使用 Google Gemini)。
* **文字轉語音 (TTS):** 將AI的文字回覆轉換成語音播放給使用者 (使用 Google Cloud Text-to-Speech)。
* **對談歷史紀錄:** 將每一次對談的輸入與輸出儲存至資料庫。

## 技術架構
* **前端:** 網頁應用程式
* **後端:** Node.js, Express
* **資料庫:** PostgreSQL
* **外部服務:** Google Cloud Platform (Speech-to-Text, Text-to-Speech, Gemini API)

## 安裝

### 1. 後端
```bash
# 進入後端目錄
cd Backend

# 安裝依賴套件
npm install
```

### 2. 資料庫
- 請確認您已安裝並執行 PostgreSQL。
- 使用 `Backend/database.sql` 檔案中的 SQL 指令來建立所需的資料庫和資料表。

### 3. 環境變數
- 在 `Backend` 目錄下，根據 `.env.example` 檔案建立一個 `.env` 檔案。
- 填入必要的環境變數，例如資料庫連線資訊和 Google Cloud API 金鑰。

## 使用方法

1. **啟動後端伺服器:**
   ```bash
   cd Backend
   node server.js
   ```
2. **開啟前端頁面:**
   - 在瀏覽器中開啟 `Frontend/index.html` 檔案。

## 貢獻

歡迎各種形式的貢獻！如果您有任何建議或發現錯誤，請隨時提出 Issue 或發送 Pull Request。

## 授權

本專案採用 [ISC](https://opensource.org/licenses/ISC) 授權。
