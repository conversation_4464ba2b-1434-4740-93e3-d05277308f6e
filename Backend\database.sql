-- 建立對談記錄表 (conversations)
CREATE TABLE conversations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    user_input_text TEXT,
    ai_response_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 建立系統設定表 (system_settings)
CREATE TABLE system_settings (
    key VARCHAR(255) PRIMARY KEY,
    value TEXT NOT NULL
);

-- 插入一些預設設定
INSERT INTO system_settings (key, value) VALUES
('google_api_key', 'YOUR_GOOGLE_API_KEY'),
('gemini_model', 'gemini-pro');
