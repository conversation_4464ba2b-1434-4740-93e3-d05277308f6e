// 引入必要的套件
const express = require('express');
const cors = require('cors');
require('dotenv').config();

// 建立 Express 應用程式
const app = express();

// 使用中介軟體
app.use(cors());
app.use(express.json());

// 基本的健康檢查路由
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// 設定監聽的埠號
const PORT = process.env.PORT || 3001;

// 啟動伺服器
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
