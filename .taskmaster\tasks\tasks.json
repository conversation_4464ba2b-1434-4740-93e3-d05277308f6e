{"master": {"tasks": [{"id": 1, "title": "專案初始化與後端伺服器設定", "description": "建立專案的基礎目錄結構，包含前端和後端，並設定一個基本的後端API伺服器作為開發起點。", "details": "1. 建立根目錄，並在其中建立 `Frontend/` 和 `Backend/` 子目錄。\n2. 進入 `Backend/` 目錄，使用 Node.js 和 Express 框架初始化專案 (`npm init -y`, `npm install express cors`)。\n3. 建立主伺服器文件 `server.js`，設定一個基本的 Express 應用，監聽指定端口（例如 3001），並啟用 CORS 中間件以允許前端訪問。\n4. 建立一個測試路由 `GET /`，回傳 `{ message: 'Backend is running' }` 以供驗證。", "testStrategy": "啟動後端伺服器 (`node server.js`)，使用 curl 或瀏覽器訪問 `http://localhost:3001`，確認收到 JSON 回應，表示伺服器設定成功。", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "資料庫結構設計與設定", "description": "根據PRD要求，設計並在PostgreSQL中建立儲存對談記錄和系統設定所需的資料表。", "details": "1. 安裝並設定本地或雲端的 PostgreSQL 伺服器。\n2. 設計 `conversations` 資料表，欄位應包含：`id` (SERIAL PRIMARY KEY), `session_id` (VARCHAR(255)), `user_input` (TEXT), `ai_response` (TEXT), `created_at` (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)。\n3. 設計 `system_settings` 資料表，用於儲存API金鑰等，欄位包含：`key` (VARCHAR(255) PRIMARY KEY), `value` (TEXT)。\n4. 使用 SQL 腳本或 ORM (如 Prisma 或 Sequelize) 來執行資料庫遷移，建立上述資料表。", "testStrategy": "使用資料庫管理工具（如 DBeaver 或 pgAdmin）連接到資料庫，驗證 `conversations` 和 `system_settings` 資料表及其欄位是否已按設計正確建立。嘗試手動插入一筆測試資料以確保完整性。", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "語音轉文字 (STT) API 端點開發", "description": "建立一個後端API端點，該端點接收前端傳來的語音檔案，並使用Google Cloud Speech-to-Text API將其轉換為文字。", "details": "1. 在後端專案中安裝 Google Cloud Speech-to-Text 客戶端函式庫 (`npm install @google-cloud/speech`)。\n2. 設定 Google Cloud 服務帳號認證，並將金鑰檔案路徑設定為環境變數 `GOOGLE_APPLICATION_CREDENTIALS`。\n3. 建立一個新的 RESTful API 端點 `POST /api/speech-to-text`，使用 `multer` 等中介軟體處理 `multipart/form-data` 格式的語音檔案上傳。\n4. 在該端點的處理邏輯中，讀取上傳的音訊檔案緩衝區，呼叫 Speech-to-Text API 進行辨識，並將辨識結果的文字以 JSON 格式回傳。", "testStrategy": "使用 Postman 或 Insomnia 等 API 測試工具，向 `/api/speech-to-text` 端點發送一個包含預錄音訊檔案（如 `.wav` 或 `.flac`）的 POST 請求。驗證回傳的 JSON 物件中是否包含正確的辨識文字。", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "AI對談引擎 (Gemini) API 端點開發", "description": "建立一個後端API端點，該端點接收文字輸入，並透過呼叫Google Gemini API來生成AI的回覆。", "details": "1. 在後端專案中安裝 Google AI Generative Language SDK (`npm install @google/generative-ai`)。\n2. 從 Google AI Studio 獲取您的 Gemini API 金鑰，並將其設定為環境變數 `GEMINI_API_KEY`。\n3. 建立一個新的 RESTful API 端點 `POST /api/chat`，接收包含 `{ prompt: '使用者輸入的文字' }` 的 JSON 請求主體。\n4. 在處理邏輯中，初始化 Gemini Pro 模型，並將收到的提示詞傳送給模型。將模型生成的回覆文字以 JSON 格式回傳。", "testStrategy": "使用 Postman 或 curl，向 `/api/chat` 端點發送一個包含文字提示的 POST 請求。驗證回傳的 JSON 物件中是否包含來自 Gemini 的合理文字回覆。", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 5, "title": "文字轉語音 (TTS) API 端點開發", "description": "建立一個後端API端點，該端點接收文字，並使用Google Cloud Text-to-Speech API將其轉換為可播放的語音檔案或串流。", "details": "1. 在後端專案中安裝 Google Cloud Text-to-Speech 客戶端函式庫 (`npm install @google-cloud/text-to-speech`)。\n2. 建立一個新的 RESTful API 端點 `POST /api/text-to-speech`，接收包含 `{ text: '要轉換的文字' }` 的 JSON 請求主體。\n3. 在處理邏輯中，呼叫 Text-to-Speech API，將文字轉換為音訊。設定語音選項（如語言、聲音）。\n4. 將 API 回傳的音訊內容（`audioContent`）以 `audio/mpeg` 格式的串流直接回傳給客戶端。", "testStrategy": "使用 Postman 或 curl，向 `/api/text-to-speech` 端點發送一個包含文字的 POST 請求。將回傳的二進位資料儲存為 `.mp3` 檔案，並嘗試播放，確認內容是否為正確的語音。", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 6, "title": "整合核心對談流程與資料庫紀錄", "description": "整合STT、Gemini和TTS服務，建立一個統一的對談API端點，處理完整的對談流程，並將每次對談記錄儲存到資料庫。", "details": "1. 建立一個主 API 端點 `POST /api/conversation`，接收前端的語音檔案。\n2. 實作處理流程：\n   a. 呼叫內部 STT 服務將語音轉為文字。\n   b. 呼叫內部 Gemini 服務，將辨識出的文字作為提示詞，取得 AI 回覆文字。\n   c. 將使用者輸入文字和 AI 回覆文字存入 PostgreSQL 的 `conversations` 表。\n   d. 呼叫內部 TTS 服務將 AI 回覆文字轉為語音。\n   e. 將最終的語音串流回傳給前端。", "testStrategy": "透過 API 測試工具向 `/api/conversation` 發送語音檔案，驗證是否能收到語音串流回覆。同時，查詢資料庫確認對應的對談紀錄是否已成功新增。", "priority": "high", "dependencies": [2, 3, 4, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "前端UI/UX基礎建設", "description": "建立前端應用的基本HTML、CSS和JavaScript結構，實現一個簡潔直觀的UI介面，包含錄音按鈕和對話顯示區域。", "details": "1. 在 `Frontend/` 目錄下，建立 `index.html`, `style.css`, `script.js`。\n2. 在 `index.html` 中，設計頁面佈局，包含一個主要的錄音按鈕（例如一個麥克風圖示）、一個顯示系統狀態的文字區域（例如 \"請點擊麥克風開始對話\"），以及一個用於顯示對話歷史的容器。\n3. 使用 CSS Flexbox 或 Grid 實現響應式設計，確保介面在桌面和行動裝置上都能良好適應。", "testStrategy": "在瀏覽器中打開 `index.html` 檔案。驗證所有 UI 元素是否按預期顯示。縮放瀏覽器視窗，檢查佈局是否能正確地響應變化。", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 8, "title": "前端麥克風權限與語音錄製功能", "description": "在前端實作獲取使用者麥克風權限的邏輯，並使用瀏覽器API錄製使用者的語音。", "details": "1. 在 `script.js` 中，為錄音按鈕添加點擊事件監聽器。\n2. 在事件處理函式中，使用 `navigator.mediaDevices.getUserMedia({ audio: true })` 來請求麥克風權限。\n3. 成功獲取權限後，實例化 `MediaRecorder` API 來開始錄音。\n4. 實現按住錄音、放開停止的互動邏輯。將錄製的音訊數據收集到一個 `chunks` 陣列中，並在停止時將其合併成一個 `Blob` 物件。", "testStrategy": "在網頁上點擊錄音按鈕，確認瀏覽器彈出麥克風授權請求。授權後，錄製一段幾秒鐘的音訊，並在瀏覽器開發者工具中確認已成功生成一個有效的 `Blob` 物件。", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "前後端整合與完整對談流程實現", "description": "將前端錄製的語音傳送至後端整合API，並接收、播放AI的語音回覆，完成端對端的對談流程。", "details": "1. 當語音錄製完成並生成 `Blob` 物件後，使用 `FormData` 將其包裝。\n2. 使用 `fetch` API 向後端的 `/api/conversation` 端點發送一個 `POST` 請求，請求主體為該 `FormData`。\n3. 處理後端的回應：將接收到的音訊串流轉換為 `Blob`，然後使用 `URL.createObjectURL()` 創建一個可播放的 URL。\n4. 將此 URL 賦值給一個動態創建的 `<audio>` 元素的 `src` 屬性，並呼叫其 `.play()` 方法自動播放AI的回覆。", "testStrategy": "進行完整的端對端手動測試。打開網頁，點擊錄音按鈕說一句話，放開按鈕後，驗證是否能聽到AI的語音回覆。檢查瀏覽器開發者工具的網路分頁，確認API請求和回應皆為成功狀態 (HTTP 200)。", "priority": "high", "dependencies": [6, 8], "status": "pending", "subtasks": []}, {"id": 10, "title": "MVP部署與端對端測試", "description": "將前後端應用程式與資料庫部署到雲端平台（如Google Cloud Platform），並進行全面的端對端測試以確保MVP功能穩定可用。", "details": "1. 將後端應用程式容器化（使用 Dockerfile）。\n2. 在 Google Cloud Platform 上，設定 Cloud Run 用於部署後端容器，設定 Cloud SQL for PostgreSQL 作為資料庫。\n3. 將前端靜態文件部署到 Firebase Hosting 或 Google Cloud Storage。\n4. 設定所有服務的環境變數（資料庫連線資訊、API金鑰等）。\n5. 確保網路設定正確，例如 Cloud Run 服務可以公開訪問，且前端可以呼叫後端API。", "testStrategy": "透過公開的網域名稱訪問應用程式。執行完整的使用者流程，包括語音輸入、等待AI回覆、播放語音輸出。邀請少量測試使用者進行體驗，並收集反饋。檢查 Cloud Logging 和 Monitoring 以監控應用程式的健康狀況和錯誤。", "priority": "high", "dependencies": [9], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-14T07:29:57.437Z", "updated": "2025-07-14T07:29:57.438Z", "description": "Tasks for master context"}}}